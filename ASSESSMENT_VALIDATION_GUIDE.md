# Assessment Validation Guide

## Overview
This document provides complete validation requirements for the ATMA assessment submission endpoint to prevent validation failures.

## Validation Requirements

### RIASEC Assessment (6 dimensions)
All 6 dimensions are **required** with integer values between 0-100:
- `realistic`
- `investigative` 
- `artistic`
- `social`
- `enterprising`
- `conventional`

### OCEAN Assessment (5 dimensions)
All 5 dimensions are **required** with integer values between 0-100:
- `openness`
- `conscientiousness`
- `extraversion`
- `agreeableness`
- `neuroticism`

### VIA-IS Assessment (24 character strengths)
All 24 character strengths are **required** with integer values between 0-100:

#### Wisdom and Knowledge
- `creativity`
- `curiosity`
- `judgment`
- `loveOfLearning`
- `perspective`

#### Courage
- `bravery`
- `perseverance`
- `honesty`
- `zest`

#### Humanity
- `love`
- `kindness`
- `socialIntelligence`

#### Justice
- `teamwork`
- `fairness`
- `leadership`

#### Temperance
- `forgiveness`
- `humility`
- `prudence`
- `selfRegulation`

#### Transcendence
- `appreciationOfBeauty`
- `gratitude`
- `hope`
- `humor`
- `spirituality`

## Complete Valid Example

```json
{
  "riasec": {
    "realistic": 75,
    "investigative": 85,
    "artistic": 60,
    "social": 50,
    "enterprising": 70,
    "conventional": 55
  },
  "ocean": {
    "openness": 80,
    "conscientiousness": 65,
    "extraversion": 55,
    "agreeableness": 45,
    "neuroticism": 30
  },
  "viaIs": {
    "creativity": 85,
    "curiosity": 78,
    "judgment": 70,
    "loveOfLearning": 82,
    "perspective": 60,
    "bravery": 55,
    "perseverance": 68,
    "honesty": 73,
    "zest": 66,
    "love": 80,
    "kindness": 75,
    "socialIntelligence": 65,
    "teamwork": 60,
    "fairness": 70,
    "leadership": 67,
    "forgiveness": 58,
    "humility": 62,
    "prudence": 69,
    "selfRegulation": 61,
    "appreciationOfBeauty": 50,
    "gratitude": 72,
    "hope": 77,
    "humor": 65,
    "spirituality": 55
  }
}
```

## Common Validation Errors

### Missing VIA-IS Fields
**Error**: Previously, documentation examples only included 5 VIA-IS fields instead of all 24 required fields.
**Solution**: Always include all 24 character strengths in the `viaIs` object.

### Invalid Value Ranges
**Error**: Values outside 0-100 range or non-integer values.
**Solution**: Ensure all values are integers between 0 and 100 inclusive.

### Missing Required Sections
**Error**: Missing `riasec`, `ocean`, or `viaIs` sections.
**Solution**: All three assessment types must be included in every submission.

## Testing Your Payload

Before submitting to production, validate your payload structure:

1. **Count VIA-IS fields**: Must have exactly 24 fields
2. **Check value ranges**: All values must be 0-100 integers
3. **Verify required sections**: Must include riasec, ocean, and viaIs
4. **Test with validation**: Use the test endpoint first: `POST /api/assessment/test/submit`

## Updated Documentation

The following files have been updated with complete VIA-IS schemas:
- `assessment-service/Assessment_API.md`
- `api-gateway/API_Gateway_Documentation.md`
- `api-gateway/USAGE_GUIDE.md`
- `test-integration.md`

## Validation Schema Location

The complete validation schemas are defined in:
- `assessment-service/src/schemas/assessment.js`

## Test Data

Complete test data examples can be found in:
- `archive-service/test-data/sample-assessment.json`
- `assessment-service/tests/schemas.test.js`
