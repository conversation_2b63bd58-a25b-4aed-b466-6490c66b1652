const rateLimit = require('express-rate-limit');
const config = require('../config');

/**
 * Rate limiter umum untuk semua endpoint
 */
const generalLimiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.maxRequests,
  message: {
    success: false,
    error: 'RATE_LIMIT_EXCEEDED',
    message: 'Too many requests from this IP, please try again later.',
    retryAfter: Math.ceil(config.rateLimit.windowMs / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false,
  
  // Skip internal services
  skip: (req) => {
    return req.isInternalService === true;
  },
  
  // Custom key generator (bisa berdasarkan IP + user ID)
  keyGenerator: (req) => {
    if (req.user && req.user.id) {
      return `${req.ip}-${req.user.id}`;
    }
    return req.ip;
  }
});

/**
 * Rate limiter khusus untuk authentication endpoints
 */
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 requests per windowMs
  message: {
    success: false,
    error: 'AUTH_RATE_LIMIT_EXCEEDED',
    message: 'Too many authentication attempts, please try again later.',
    retryAfter: 900 // 15 minutes in seconds
  },
  standardHeaders: true,
  legacyHeaders: false,
  
  skip: (req) => {
    return req.isInternalService === true;
  }
});

/**
 * Rate limiter khusus untuk assessment submission
 */
const assessmentLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // Limit each user to 5 assessment submissions per hour
  message: {
    success: false,
    error: 'ASSESSMENT_RATE_LIMIT_EXCEEDED',
    message: 'Too many assessment submissions, please try again later.',
    retryAfter: 3600 // 1 hour in seconds
  },
  standardHeaders: true,
  legacyHeaders: false,
  
  skip: (req) => {
    return req.isInternalService === true;
  },
  
  keyGenerator: (req) => {
    if (req.user && req.user.id) {
      return `assessment-${req.user.id}`;
    }
    return `assessment-${req.ip}`;
  }
});

/**
 * Rate limiter khusus untuk admin endpoints
 */
const adminLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // Higher limit for admin users
  message: {
    success: false,
    error: 'ADMIN_RATE_LIMIT_EXCEEDED',
    message: 'Too many admin requests, please try again later.',
    retryAfter: 900
  },
  standardHeaders: true,
  legacyHeaders: false,
  
  skip: (req) => {
    return req.isInternalService === true;
  }
});

module.exports = {
  generalLimiter,
  authLimiter,
  assessmentLimiter,
  adminLimiter
};
